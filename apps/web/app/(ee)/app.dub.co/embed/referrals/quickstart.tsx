import { Program } from "@dub/prisma/client";
import {
  Button,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNavBar,
  Check,
  Copy,
  useCopyToClipboard,
  useMediaQuery,
} from "@dub/ui";
import { cn, DUB_LOGO, TAB_ITEM_ANIMATION_SETTINGS } from "@dub/utils";
import { motion } from "framer-motion";
import { ReferralsEmbedLink } from "./types";

const BUTTON_CLASSNAME = "h-9 rounded-lg bg-bg-inverted hover:bg-neutral-800";

export function ReferralsEmbedQuickstart({
  program,
  link,
  onViewResources,
}: {
  program: Program;
  link: ReferralsEmbedLink;
  onViewResources?: () => void;
}) {
  const [copied, copyToClipboard] = useCopyToClipboard();
  const { isMobile } = useMediaQuery();
  const payoutsDisabled = link.saleAmount === 0;

  const items = [
    {
      title: "Share your link",
      description: `Sharing is caring! Recommend ${program.name} to all your friends, family, and social followers.`,
      illustration: <ShareLink />,
      cta: (
        <Button
          className={BUTTON_CLASSNAME}
          onClick={() => copyToClipboard(link.shortLink)}
          text={copied ? "Copied link" : "Copy link"}
          icon={
            <div className="relative size-4">
              <div
                className={cn(
                  "absolute inset-0 transition-[transform,opacity]",
                  copied && "translate-y-1 opacity-0",
                )}
              >
                <Copy className="size-4" />
              </div>
              <div
                className={cn(
                  "absolute inset-0 transition-[transform,opacity]",
                  !copied && "translate-y-1 opacity-0",
                )}
              >
                <Check className="size-4" />
              </div>
            </div>
          }
        />
      ),
    },
    {
      title: "Success kit",
      description:
        "Make sure you get setup for success with the official brand files and supportive content and documents.",
      illustration: <SuccessKit logo={program.logo ?? DUB_LOGO} />,
      cta: (
        <Button
          className="h-9 rounded-lg"
          text={onViewResources ? "View resources" : "No resources"}
          disabled={!onViewResources}
          onClick={onViewResources}
        />
      ),
    },
    {
      title: "Receive earnings",
      description:
        "After you payouts are connected, you'll get paid out automatically for all your sales.",
      illustration: <ConnectPayouts logo={program.logo ?? DUB_LOGO} />,
      cta: (
        <Button
          className={payoutsDisabled ? "h-9 rounded-lg" : BUTTON_CLASSNAME}
          disabledTooltip={
            payoutsDisabled
              ? "You will be able to withdraw your earnings once you have made at least one sale."
              : undefined
          }
          onClick={() =>
            window.open("https://partners.dub.co/settings/payouts", "_blank")
          }
          text="Connect payouts"
        />
      ),
    },
  ];

  return (
    <motion.div
      className="border-border-muted bg-bg-default rounded-lg border p-2"
      {...TAB_ITEM_ANIMATION_SETTINGS}
    >
      {isMobile ? (
        <Carousel>
          <CarouselContent>
            {items.map((item) => (
              <CarouselItem
                key={item.title}
                className="bg-bg-muted flex flex-col items-center justify-between gap-4 rounded-lg p-8 text-center"
              >
                {item.illustration}
                <h3 className="text-content-emphasis text-lg font-medium">
                  {item.title}
                </h3>
                <p className="text-content-subtle text-pretty text-sm">
                  {item.description}
                </p>
                {item.cta}
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselNavBar variant="simple" />
        </Carousel>
      ) : (
        <div className="grid grid-cols-3 gap-4">
          {items.map((item) => (
            <div
              key={item.title}
              className="bg-bg-muted flex flex-col items-center justify-between gap-4 rounded-lg p-8 text-center"
            >
              {item.illustration}
              <h3 className="text-content-emphasis text-lg font-medium">
                {item.title}
              </h3>
              <p className="text-content-subtle text-pretty text-sm">
                {item.description}
              </p>
              {item.cta}
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
}

const BG_MUTED = "rgb(var(--bg-muted))";
const BG_DEFAULT = "rgb(var(--bg-default))";
const BORDER_SUBTLE = "rgb(var(--border-subtle))";
const CONTENT_SUBTLE = "rgb(var(--content-subtle))";

const ShareLink = () => {
  return (
    <svg
      width="194"
      height="121"
      viewBox="0 0 194 121"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-auto w-full"
    >
      <rect x="33" y="12" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="33"
        y="12"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="65" y="12" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="65"
        y="12"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M76.0223 34.4001H85.9779C86.7634 34.4001 87.4001 33.7634 87.4001 32.9779V23.0223C87.4001 22.2368 86.7634 21.6001 85.9779 21.6001H76.0223C75.2368 21.6001 74.6001 22.2368 74.6001 23.0223V32.9779C74.6001 33.7634 75.2368 34.4001 76.0223 34.4001Z"
        fill={CONTENT_SUBTLE}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M85.6229 32.6229H83.7234V29.3877C83.7234 28.5007 83.3864 28.005 82.6843 28.005C81.9205 28.005 81.5216 28.5209 81.5216 29.3877V32.6229H79.691V26.4599H81.5216V27.2901C81.5216 27.2901 82.072 26.2716 83.3797 26.2716C84.687 26.2716 85.6229 27.0699 85.6229 28.7209V32.6229ZM77.5072 25.6529C76.8837 25.6529 76.3784 25.1437 76.3784 24.5157C76.3784 23.8876 76.8837 23.3784 77.5072 23.3784C78.1307 23.3784 78.6357 23.8876 78.6357 24.5157C78.6357 25.1437 78.1307 25.6529 77.5072 25.6529ZM76.562 32.6229H78.4708V26.4599H76.562V32.6229Z"
        fill={BG_DEFAULT}
      />
      <rect x="97" y="12" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="97"
        y="12"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="129" y="12" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="129"
        y="12"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <g clipPath="url(#clip0_4494_38695)">
        <path
          d="M147.966 24.2374C146.993 22.6321 145.234 21.5557 143.219 21.5557C140.153 21.5557 137.667 24.0419 137.667 27.1094C137.667 28.1192 137.94 29.0641 138.412 29.8801C138.741 30.4979 138.372 31.9574 137.667 32.6623C138.624 32.7139 139.886 32.2819 140.449 31.9166C140.823 32.1326 141.418 32.4197 142.195 32.5663C142.294 32.585 142.401 32.5814 142.502 32.5948"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M148.333 26.4409C150.542 26.4409 152.333 28.232 152.333 30.4409C152.333 31.1689 152.136 31.8489 151.797 32.4374C151.559 32.8818 151.826 33.9334 152.333 34.4418C151.644 34.4791 150.735 34.1671 150.329 33.9049C150.06 34.0605 149.631 34.2667 149.071 34.3734C148.832 34.4187 148.585 34.4427 148.333 34.4427C146.123 34.4427 144.333 32.6516 144.333 30.4427C144.333 28.2329 146.124 26.4427 148.333 26.4427L148.333 26.4409Z"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <rect x="33" y="44" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="33"
        y="44"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <g clipPath="url(#clip1_4494_38695)">
        <path
          d="M42.5557 57.1108L48.5708 60.4291C48.8383 60.5766 49.1619 60.5766 49.4294 60.4291L55.4446 57.1108"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M54.1113 62L56.3336 64.2222L54.1113 66.4444"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M55.4446 60.2344V56.6664C55.4446 55.6851 54.649 54.8887 53.6668 54.8887H44.3334C43.3512 54.8887 42.5557 55.6851 42.5557 56.6664V63.3331C42.5557 64.3144 43.3512 65.1109 44.3334 65.1109H49.8543"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M56.3336 64.2222H51.8892"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <rect x="65" y="44" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="65"
        y="44"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="97" y="44" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="97"
        y="44"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <path
        d="M114.212 59.0206L118.974 53.6001H117.846L113.709 58.3057L110.408 53.6001H106.599L111.592 60.7165L106.599 66.4H107.727L112.093 61.4297L115.58 66.4H119.388M108.134 54.4331H109.867L117.845 65.6079H116.111"
        fill={CONTENT_SUBTLE}
      />
      <rect x="129" y="44" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="129"
        y="44"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="33" y="76" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="33"
        y="76"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="65" y="76" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="65"
        y="76"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <path
        d="M76.3335 94.1841V96.5876C76.3335 96.9521 76.5557 97.2792 76.8944 97.4134L78.6233 98.1005C78.9788 98.2419 79.3842 98.1396 79.6313 97.8481L81.1122 96.089"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M85.0001 97.5554C86.3501 97.5554 87.4446 95.0681 87.4446 91.9999C87.4446 88.9316 86.3501 86.4443 85.0001 86.4443C83.6501 86.4443 82.5557 88.9316 82.5557 91.9999C82.5557 95.0681 83.6501 97.5554 85.0001 97.5554Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M84.5148 97.4445L75.3201 93.7796C75.0926 93.6916 74.9139 93.5165 74.8188 93.2916C74.6872 92.9805 74.5557 92.5387 74.5557 92C74.5557 91.7591 74.5823 91.2738 74.8126 90.7236C74.9086 90.4951 75.0908 90.3094 75.3219 90.2205C78.5557 88.968 81.281 87.8071 84.5148 86.5547"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M85.8891 91.9998C85.8891 91.2638 85.2917 90.6665 84.5557 90.6665C84.5086 90.6665 84.4642 90.6754 84.4179 90.6807C84.3664 91.0825 84.3335 91.5207 84.3335 91.9998C84.3335 92.4789 84.3664 92.9172 84.4179 93.3189C84.4642 93.3234 84.5086 93.3332 84.5557 93.3332C85.2917 93.3332 85.8891 92.7358 85.8891 91.9998Z"
        fill="#737373"
      />
      <rect x="97" y="76" width="32" height="32" rx="6" fill={BG_MUTED} />
      <rect
        x="97"
        y="76"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <rect x="129" y="76" width="32" height="32" rx="6" fill={BG_DEFAULT} />
      <rect
        x="129"
        y="76"
        width="32"
        height="32"
        rx="6"
        stroke={BORDER_SUBTLE}
      />
      <path
        d="M143 86.4443H140.333C139.842 86.4443 139.444 86.8423 139.444 87.3332V89.9999C139.444 90.4908 139.842 90.8888 140.333 90.8888H143C143.491 90.8888 143.889 90.4908 143.889 89.9999V87.3332C143.889 86.8423 143.491 86.4443 143 86.4443Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M149.666 86.4443H147C146.509 86.4443 146.111 86.8423 146.111 87.3332V89.9999C146.111 90.4908 146.509 90.8888 147 90.8888H149.666C150.157 90.8888 150.555 90.4908 150.555 89.9999V87.3332C150.555 86.8423 150.157 86.4443 149.666 86.4443Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M143 93.1108H140.333C139.842 93.1108 139.444 93.5088 139.444 93.9997V96.6664C139.444 97.1573 139.842 97.5553 140.333 97.5553H143C143.491 97.5553 143.889 97.1573 143.889 96.6664V93.9997C143.889 93.5088 143.491 93.1108 143 93.1108Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M141.5 88.8333V88.5H141.833V88.8333H141.5Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M148.167 88.8333V88.5H148.5V88.8333H148.167Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M141.5 95.4998V95.1665H141.833V95.4998H141.5Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M150.389 97.722V97.3887H150.722V97.722H150.389Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M149.056 96.389V96.0557H149.389V96.389H149.056Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M150.389 95.0555V94.7222H150.722V95.0555H150.389Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M147.278 97.722V97.3887H148.056V97.722H147.278Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M145.944 96.3888V94.7222H146.278V96.3888H145.944Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <path
        d="M147.278 93.722V93.3887H149.389V93.722H147.278Z"
        fill="#737373"
        stroke={CONTENT_SUBTLE}
      />
      <defs>
        <clipPath id="clip0_4494_38695">
          <rect
            width="16"
            height="16"
            fill={BG_DEFAULT}
            transform="translate(137 20)"
          />
        </clipPath>
        <clipPath id="clip1_4494_38695">
          <rect
            width="16"
            height="16"
            fill={BG_DEFAULT}
            transform="translate(41 52)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

const SuccessKit = ({ logo }: { logo: string }) => {
  return (
    <svg
      width="194"
      height="121"
      viewBox="0 0 194 121"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-auto w-full"
    >
      <rect width="194" height="121" fill={BG_MUTED} />
      <circle
        cx="119"
        cy="23"
        r="15.5"
        fill={BG_MUTED}
        stroke={BORDER_SUBTLE}
      />
      <rect x="78" y="41" width="38" height="38" rx="19" fill={BG_DEFAULT} />
      <rect
        x="78"
        y="41"
        width="38"
        height="38"
        rx="19"
        stroke={BORDER_SUBTLE}
      />
      <rect
        x="82"
        y="45"
        width="30"
        height="30"
        rx="15"
        fill="url(#pattern0_4494_38753)"
      />
      <rect
        x="125.5"
        y="44.5"
        width="31"
        height="31"
        rx="15.5"
        fill={BG_DEFAULT}
      />
      <rect
        x="125.5"
        y="44.5"
        width="31"
        height="31"
        rx="15.5"
        stroke={BORDER_SUBTLE}
      />
      <g clipPath="url(#clip0_4494_38753)">
        <path
          d="M136.148 55.2096L134.589 59.2813C134.452 59.6406 134.756 60.0136 135.135 59.9514L139.471 59.2416C139.85 59.1794 140.019 58.7289 139.774 58.4322L136.998 55.0704C136.755 54.776 136.285 54.8529 136.148 55.2096Z"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M145 58.4446C146.35 58.4446 147.445 57.3501 147.445 56.0001C147.445 54.6501 146.35 53.5557 145 53.5557C143.65 53.5557 142.556 54.6501 142.556 56.0001C142.556 57.3501 143.65 58.4446 145 58.4446Z"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M145.667 63.1263C144.534 63.9455 143.698 65.1099 143.284 66.4448C142.465 65.3123 141.3 64.4761 139.965 64.0617C141.098 63.2425 141.934 62.078 142.348 60.7432C143.168 61.8756 144.332 62.7118 145.667 63.1263Z"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <rect
        x="60.5"
        y="7.5"
        width="31"
        height="31"
        rx="15.5"
        fill={BG_DEFAULT}
      />
      <rect
        x="60.5"
        y="7.5"
        width="31"
        height="31"
        rx="15.5"
        stroke={BORDER_SUBTLE}
      />
      <g clipPath="url(#clip1_4494_38753)">
        <path
          d="M73.1108 21H74.8886"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M73.1108 23.667H77.1108"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M81.4757 20.5558H78.4446C77.9539 20.5558 77.5557 20.1576 77.5557 19.6669V16.6465"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M81.5554 25.0001V20.9237C81.5554 20.6881 81.4621 20.4614 81.295 20.2952L77.8159 16.8161C77.6488 16.649 77.423 16.5557 77.1874 16.5557H72.2221C71.2399 16.5557 70.4443 17.3521 70.4443 18.3334V27.6668C70.4443 28.6481 71.2399 29.4446 72.2221 29.4446H76.7101"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M78.8833 29.0002L80.3135 30.3335L83.3331 26.3335"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <rect
        x="60.5"
        y="82.5"
        width="31"
        height="31"
        rx="15.5"
        fill={BG_DEFAULT}
      />
      <rect
        x="60.5"
        y="82.5"
        width="31"
        height="31"
        rx="15.5"
        stroke={BORDER_SUBTLE}
      />
      <g clipPath="url(#clip2_4494_38753)">
        <path
          d="M75.3809 104.415C72.023 104.095 69.4166 101.198 69.5614 97.7244C69.701 94.377 72.5393 91.6129 75.8892 91.5566C79.4992 91.4959 82.4445 94.4041 82.4445 98.0001C82.4445 99.3501 81.3501 100.445 80.0001 100.445H77.3663C76.4475 100.445 75.8616 101.425 76.2972 102.234L76.5083 102.626C76.7391 103.055 76.6915 103.58 76.3873 103.961C76.1441 104.265 75.7684 104.452 75.3809 104.415Z"
          stroke={CONTENT_SUBTLE}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M76.0002 95.3334C76.4911 95.3334 76.8891 94.9355 76.8891 94.4446C76.8891 93.9536 76.4911 93.5557 76.0002 93.5557C75.5093 93.5557 75.1113 93.9536 75.1113 94.4446C75.1113 94.9355 75.5093 95.3334 76.0002 95.3334Z"
          fill="#737373"
        />
        <path
          d="M73.4861 96.3749C73.977 96.3749 74.3749 95.977 74.3749 95.4861C74.3749 94.9951 73.977 94.5972 73.4861 94.5972C72.9951 94.5972 72.5972 94.9951 72.5972 95.4861C72.5972 95.977 72.9951 96.3749 73.4861 96.3749Z"
          fill="#737373"
        />
        <path
          d="M78.5144 96.3749C79.0053 96.3749 79.4033 95.977 79.4033 95.4861C79.4033 94.9951 79.0053 94.5972 78.5144 94.5972C78.0235 94.5972 77.6255 94.9951 77.6255 95.4861C77.6255 95.977 78.0235 96.3749 78.5144 96.3749Z"
          fill="#737373"
        />
        <path
          d="M72.4446 98.8891C72.9355 98.8891 73.3334 98.4911 73.3334 98.0002C73.3334 97.5093 72.9355 97.1113 72.4446 97.1113C71.9536 97.1113 71.5557 97.5093 71.5557 98.0002C71.5557 98.4911 71.9536 98.8891 72.4446 98.8891Z"
          fill="#737373"
        />
      </g>
      <circle
        cx="119"
        cy="98"
        r="15.5"
        fill={BG_MUTED}
        stroke={BORDER_SUBTLE}
      />
      <circle cx="54" cy="60" r="15.5" fill={BG_MUTED} stroke={BORDER_SUBTLE} />
      <defs>
        <pattern
          id="pattern0_4494_38753"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_4494_38753" transform="scale(0.0025)" />
        </pattern>
        <clipPath id="clip0_4494_38753">
          <rect
            width="16"
            height="16"
            fill={BG_DEFAULT}
            transform="translate(133 52)"
          />
        </clipPath>
        <clipPath id="clip1_4494_38753">
          <rect
            width="16"
            height="16"
            fill={BG_DEFAULT}
            transform="translate(68 15)"
          />
        </clipPath>
        <clipPath id="clip2_4494_38753">
          <rect
            width="16"
            height="16"
            fill={BG_DEFAULT}
            transform="translate(68 90)"
          />
        </clipPath>
        <image
          id="image0_4494_38753"
          width="400"
          height="400"
          xlinkHref={logo}
        />
      </defs>
    </svg>
  );
};

const ConnectPayouts = ({ logo }: { logo: string }) => {
  return (
    <svg
      width="194"
      height="121"
      viewBox="0 0 194 121"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-auto w-full"
    >
      <rect width="194" height="121" fill={BG_MUTED} />
      <rect
        x="33.5"
        y="1.5"
        width="127"
        height="84"
        rx="10.5"
        stroke={BORDER_SUBTLE}
      />
      <rect
        x="40.5"
        y="8.5"
        width="113"
        height="70"
        rx="5.5"
        fill={BG_DEFAULT}
      />
      <rect
        x="40.5"
        y="8.5"
        width="113"
        height="70"
        rx="5.5"
        stroke={BORDER_SUBTLE}
      />
      <rect
        x="48"
        y="16"
        width="14"
        height="14"
        rx="7"
        fill="url(#pattern0_4494_38789)"
      />
      <path
        opacity="0.2"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M146 23.1861C146 21.2818 145.104 19.7793 143.393 19.7793C141.674 19.7793 140.634 21.2818 140.634 23.1712C140.634 25.4102 141.862 26.5409 143.624 26.5409C144.483 26.5409 145.133 26.34 145.624 26.0574V24.5697C145.133 24.8226 144.57 24.9788 143.855 24.9788C143.154 24.9788 142.533 24.7259 142.454 23.8481H145.986C145.986 23.7514 146 23.3646 146 23.1861ZM142.432 22.4794C142.432 21.6389 142.931 21.2893 143.386 21.2893C143.826 21.2893 144.296 21.6389 144.296 22.4794H142.432ZM137.846 19.7793C137.138 19.7793 136.683 20.1214 136.431 20.3595L136.337 19.8983H134.748V28.5716L136.553 28.1773L136.561 26.0722C136.821 26.2656 137.203 26.5409 137.839 26.5409C139.132 26.5409 140.309 25.4697 140.309 23.1117C140.302 20.9546 139.11 19.7793 137.846 19.7793ZM137.413 24.9044C136.987 24.9044 136.734 24.7482 136.561 24.5548L136.553 21.7951C136.741 21.5794 137.001 21.4306 137.413 21.4306C138.07 21.4306 138.525 22.1893 138.525 23.1638C138.525 24.1605 138.077 24.9044 137.413 24.9044ZM132.263 19.3404L134.076 18.9387V17.4287L132.263 17.823V19.3404ZM132.263 19.9057H134.076V26.4144H132.263V19.9057ZM130.321 20.4562L130.205 19.9057H128.645V26.4144H130.451V22.0034C130.877 21.4306 131.599 21.5348 131.823 21.6166V19.9057C131.592 19.8165 130.747 19.6528 130.321 20.4562ZM126.709 18.2916L124.947 18.6784L124.94 24.6366C124.94 25.7375 125.742 26.5483 126.811 26.5483C127.403 26.5483 127.836 26.4367 128.074 26.3028V24.7928C127.843 24.8895 126.702 25.2317 126.702 24.1308V21.4901H128.074V19.9057H126.702L126.709 18.2916ZM121.827 21.7951C121.827 21.505 122.058 21.3934 122.441 21.3934C122.99 21.3934 123.683 21.5645 124.232 21.8695V20.1214C123.633 19.876 123.041 19.7793 122.441 19.7793C120.975 19.7793 120 20.5678 120 21.8844C120 23.9374 122.744 23.6101 122.744 24.4953C122.744 24.8374 122.456 24.949 122.051 24.949C121.452 24.949 120.686 24.6961 120.079 24.3539V26.1243C120.751 26.4218 121.43 26.5483 122.051 26.5483C123.553 26.5483 124.586 25.7821 124.586 24.4506C124.579 22.234 121.827 22.6282 121.827 21.7951Z"
        fill="#0A2540"
      />
      <path
        d="M48.4304 58V53.6364H50.0668C50.402 53.6364 50.6832 53.6989 50.9105 53.8239C51.1392 53.9489 51.3118 54.1207 51.4283 54.3395C51.5462 54.5568 51.6051 54.804 51.6051 55.081C51.6051 55.3608 51.5462 55.6094 51.4283 55.8267C51.3104 56.044 51.1364 56.2152 50.9062 56.3402C50.6761 56.4638 50.3928 56.5256 50.0561 56.5256H48.9716V55.8757H49.9496C50.1456 55.8757 50.3061 55.8416 50.4311 55.7734C50.5561 55.7053 50.6484 55.6115 50.7081 55.4922C50.7692 55.3729 50.7997 55.2358 50.7997 55.081C50.7997 54.9261 50.7692 54.7898 50.7081 54.6719C50.6484 54.554 50.5554 54.4624 50.429 54.397C50.304 54.3303 50.1428 54.2969 49.9453 54.2969H49.2209V58H48.4304ZM53.1408 58.0661C52.9334 58.0661 52.7466 58.0291 52.5804 57.9553C52.4157 57.88 52.285 57.7692 52.1884 57.6229C52.0932 57.4766 52.0456 57.2962 52.0456 57.0817C52.0456 56.897 52.0797 56.7443 52.1479 56.6236C52.2161 56.5028 52.3091 56.4062 52.427 56.3338C52.5449 56.2614 52.6777 56.2067 52.8255 56.1697C52.9746 56.1314 53.1287 56.1037 53.2878 56.0866C53.4796 56.0668 53.6351 56.049 53.7544 56.0334C53.8738 56.0163 53.9604 55.9908 54.0144 55.9567C54.0698 55.9212 54.0975 55.8665 54.0975 55.7926V55.7798C54.0975 55.6193 54.0499 55.495 53.9547 55.407C53.8596 55.3189 53.7225 55.2749 53.5435 55.2749C53.3546 55.2749 53.2047 55.3161 53.0939 55.3984C52.9846 55.4808 52.9107 55.5781 52.8723 55.6903L52.1522 55.5881C52.209 55.3892 52.3027 55.223 52.4334 55.0895C52.5641 54.9545 52.7239 54.8537 52.9128 54.7869C53.1017 54.7188 53.3105 54.6847 53.5392 54.6847C53.6969 54.6847 53.8539 54.7031 54.0101 54.7401C54.1664 54.777 54.3091 54.8381 54.4384 54.9233C54.5676 55.0071 54.6713 55.1214 54.7495 55.2663C54.829 55.4112 54.8688 55.5923 54.8688 55.8097V58H54.1273V57.5504H54.1017C54.0549 57.6413 53.9888 57.7266 53.9036 57.8061C53.8198 57.8842 53.714 57.9474 53.5861 57.9957C53.4597 58.0426 53.3113 58.0661 53.1408 58.0661ZM53.3411 57.4993C53.4959 57.4993 53.6301 57.4687 53.7438 57.4077C53.8574 57.3452 53.9448 57.2628 54.0059 57.1605C54.0684 57.0582 54.0996 56.9467 54.0996 56.826V56.4403C54.0755 56.4602 54.0343 56.4787 53.976 56.4957C53.9192 56.5128 53.8553 56.5277 53.7843 56.5405C53.7132 56.5533 53.6429 56.5646 53.5733 56.5746C53.5037 56.5845 53.4434 56.593 53.3922 56.6001C53.2772 56.6158 53.1742 56.6413 53.0833 56.6768C52.9924 56.7124 52.9206 56.7621 52.8681 56.826C52.8155 56.8885 52.7892 56.9695 52.7892 57.0689C52.7892 57.2109 52.8411 57.3182 52.9448 57.3906C53.0485 57.4631 53.1806 57.4993 53.3411 57.4993ZM56.0066 59.2273C55.9015 59.2273 55.8042 59.2188 55.7147 59.2017C55.6266 59.1861 55.5563 59.1676 55.5037 59.1463L55.6827 58.5455C55.7949 58.5781 55.8951 58.5938 55.9831 58.5923C56.0712 58.5909 56.1486 58.5632 56.2154 58.5092C56.2836 58.4567 56.3411 58.3686 56.388 58.245L56.454 58.0682L55.2672 54.7273H56.0854L56.8397 57.1989H56.8738L57.6301 54.7273H58.4505L57.1401 58.3963C57.079 58.5696 56.998 58.718 56.8972 58.8416C56.7963 58.9666 56.6728 59.0618 56.5265 59.1271C56.3816 59.1939 56.2083 59.2273 56.0066 59.2273ZM60.3004 58.0639C59.9808 58.0639 59.7038 57.9936 59.4695 57.853C59.2351 57.7124 59.0533 57.5156 58.924 57.2628C58.7962 57.0099 58.7322 56.7145 58.7322 56.3764C58.7322 56.0384 58.7962 55.7422 58.924 55.4879C59.0533 55.2337 59.2351 55.0362 59.4695 54.8956C59.7038 54.755 59.9808 54.6847 60.3004 54.6847C60.62 54.6847 60.897 54.755 61.1314 54.8956C61.3658 55.0362 61.5469 55.2337 61.6747 55.4879C61.804 55.7422 61.8686 56.0384 61.8686 56.3764C61.8686 56.7145 61.804 57.0099 61.6747 57.2628C61.5469 57.5156 61.3658 57.7124 61.1314 57.853C60.897 57.9936 60.62 58.0639 60.3004 58.0639ZM60.3047 57.446C60.478 57.446 60.6229 57.3984 60.7393 57.3033C60.8558 57.2067 60.9425 57.0774 60.9993 56.9155C61.0575 56.7536 61.0866 56.5732 61.0866 56.3743C61.0866 56.174 61.0575 55.9929 60.9993 55.831C60.9425 55.6676 60.8558 55.5376 60.7393 55.4411C60.6229 55.3445 60.478 55.2962 60.3047 55.2962C60.1271 55.2962 59.9794 55.3445 59.8615 55.4411C59.745 55.5376 59.6577 55.6676 59.5994 55.831C59.5426 55.9929 59.5142 56.174 59.5142 56.3743C59.5142 56.5732 59.5426 56.7536 59.5994 56.9155C59.6577 57.0774 59.745 57.2067 59.8615 57.3033C59.9794 57.3984 60.1271 57.446 60.3047 57.446ZM64.6092 56.6236V54.7273H65.3805V58H64.6326V57.4183H64.5985C64.5247 57.6016 64.4032 57.7514 64.2342 57.8679C64.0666 57.9844 63.8599 58.0426 63.6142 58.0426C63.3997 58.0426 63.21 57.995 63.0453 57.8999C62.8819 57.8033 62.7541 57.6634 62.6618 57.4801C62.5694 57.2955 62.5233 57.0724 62.5233 56.8111V54.7273H63.2946V56.6918C63.2946 56.8991 63.3514 57.0639 63.465 57.1861C63.5787 57.3082 63.7278 57.3693 63.9125 57.3693C64.0261 57.3693 64.1362 57.3416 64.2427 57.2862C64.3493 57.2308 64.4366 57.1484 64.5048 57.0391C64.5744 56.9283 64.6092 56.7898 64.6092 56.6236ZM67.8079 54.7273V55.3239H65.9265V54.7273H67.8079ZM66.391 53.9432H67.1623V57.0156C67.1623 57.1193 67.1779 57.1989 67.2092 57.2543C67.2418 57.3082 67.2844 57.3452 67.337 57.3651C67.3896 57.3849 67.4478 57.3949 67.5117 57.3949C67.56 57.3949 67.604 57.3913 67.6438 57.3842C67.685 57.3771 67.7163 57.3707 67.7376 57.3651L67.8675 57.968C67.8263 57.9822 67.7674 57.9979 67.6907 58.0149C67.6154 58.032 67.5231 58.0419 67.4137 58.0447C67.2205 58.0504 67.0465 58.0213 66.8917 57.9574C66.7369 57.892 66.614 57.7912 66.5231 57.6548C66.4336 57.5185 66.3896 57.348 66.391 57.1435V53.9432ZM70.9927 55.5923L70.2896 55.669C70.2697 55.598 70.2349 55.5312 70.1852 55.4688C70.1369 55.4062 70.0716 55.3558 69.9892 55.3175C69.9068 55.2791 69.8059 55.2599 69.6866 55.2599C69.5261 55.2599 69.3912 55.2947 69.2818 55.3643C69.1738 55.4339 69.1206 55.5241 69.122 55.6349C69.1206 55.7301 69.1554 55.8075 69.2264 55.8672C69.2988 55.9268 69.4181 55.9759 69.5843 56.0142L70.1426 56.1335C70.4522 56.2003 70.6824 56.3061 70.8329 56.451C70.9849 56.5959 71.0616 56.7855 71.063 57.0199C71.0616 57.2259 71.0012 57.4077 70.8819 57.5653C70.764 57.7216 70.6 57.8437 70.3897 57.9318C70.1795 58.0199 69.938 58.0639 69.6653 58.0639C69.2647 58.0639 68.9423 57.9801 68.698 57.8125C68.4537 57.6435 68.3081 57.4084 68.2612 57.1072L69.0133 57.0348C69.0474 57.1825 69.1199 57.294 69.2306 57.3693C69.3414 57.4446 69.4856 57.4822 69.6632 57.4822C69.8464 57.4822 69.9934 57.4446 70.1042 57.3693C70.2164 57.294 70.2725 57.201 70.2725 57.0902C70.2725 56.9964 70.2363 56.919 70.1639 56.858C70.0929 56.7969 69.9821 56.75 69.8315 56.7173L69.2733 56.6001C68.9593 56.5348 68.7271 56.4247 68.5765 56.2699C68.426 56.1136 68.3514 55.9162 68.3528 55.6776C68.3514 55.4759 68.4061 55.3011 68.5169 55.1534C68.6291 55.0043 68.7846 54.8892 68.9835 54.8082C69.1838 54.7259 69.4146 54.6847 69.676 54.6847C70.0595 54.6847 70.3613 54.7663 70.5815 54.9297C70.8031 55.093 70.9402 55.3139 70.9927 55.5923Z"
        fill="#171717"
      />
      <g clipPath="url(#clip0_4494_38789)">
        <path
          d="M50.54 68.1431L50.54 66.2383"
          stroke="#A3A3A3"
          strokeWidth="0.952381"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M52.6035 66.2383L52.6035 64.3335"
          stroke="#A3A3A3"
          strokeWidth="0.952381"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M52.6035 70.0479L52.6035 68.1431"
          stroke="#A3A3A3"
          strokeWidth="0.952381"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <path
        d="M60.4444 70.1219C60.0482 70.1219 59.7054 70.0102 59.4158 69.7867C59.1314 69.5632 58.9104 69.2406 58.753 68.819C58.6006 68.3924 58.5244 67.887 58.5244 67.3029C58.5244 66.7187 58.6006 66.2133 58.753 65.7867C58.9104 65.36 59.1339 65.0349 59.4235 64.8114C59.713 64.5829 60.0533 64.4686 60.4444 64.4686C60.8355 64.4686 61.1758 64.5829 61.4654 64.8114C61.7549 65.0349 61.9758 65.36 62.1282 65.7867C62.2857 66.2133 62.3644 66.7187 62.3644 67.3029C62.3644 67.887 62.2857 68.3924 62.1282 68.819C61.9758 69.2406 61.7549 69.5632 61.4654 69.7867C61.1809 70.0102 60.8406 70.1219 60.4444 70.1219ZM60.4444 69.4133C60.6831 69.4133 60.8863 69.3295 61.0539 69.1619C61.2266 68.9943 61.3562 68.753 61.4425 68.4381C61.5339 68.1181 61.5796 67.7397 61.5796 67.3029C61.5796 66.866 61.5339 66.4876 61.4425 66.1676C61.3562 65.8476 61.2266 65.6038 61.0539 65.4362C60.8812 65.2635 60.6781 65.1771 60.4444 65.1771C60.2108 65.1771 60.0076 65.2635 59.8349 65.4362C59.6622 65.6038 59.5301 65.8476 59.4387 66.1676C59.3523 66.4876 59.3092 66.866 59.3092 67.3029C59.3092 67.7397 59.3523 68.1181 59.4387 68.4381C59.5301 68.753 59.6596 68.9943 59.8273 69.1619C60 69.3295 60.2057 69.4133 60.4444 69.4133ZM61.1758 65.36L61.7854 65.3981L59.713 69.2305L59.1035 69.1924L61.1758 65.36ZM64.9976 64.4686C65.6224 64.4686 66.1024 64.6794 66.4376 65.101C66.7779 65.5175 66.9481 66.1143 66.9481 66.8914C66.9481 67.5213 66.8744 68.0775 66.7271 68.56C66.5849 69.0375 66.3538 69.4184 66.0338 69.7029C65.7138 69.9822 65.2973 70.1219 64.7843 70.1219C64.3221 70.1219 63.9513 70.0076 63.6719 69.779C63.3925 69.5454 63.197 69.2254 63.0852 68.819L63.8548 68.7505C63.9208 68.9638 64.0275 69.1289 64.1748 69.2457C64.3221 69.3575 64.5252 69.4133 64.7843 69.4133C65.0687 69.4133 65.3151 69.3321 65.5233 69.1695C65.7316 69.0019 65.8916 68.753 66.0033 68.4229C66.1151 68.0927 66.176 67.6838 66.1862 67.1962L66.3386 67.2267C66.2522 67.5111 66.0744 67.7524 65.8052 67.9505C65.536 68.1435 65.2186 68.24 64.8529 68.24C64.5024 68.24 64.1925 68.1638 63.9233 68.0114C63.6541 67.854 63.4459 67.6356 63.2986 67.3562C63.1513 67.0717 63.0776 66.7492 63.0776 66.3886C63.0776 65.9924 63.1564 65.6521 63.3138 65.3676C63.4764 65.0781 63.7024 64.8571 63.9919 64.7048C64.2865 64.5473 64.6217 64.4686 64.9976 64.4686ZM64.99 65.1771C64.6446 65.1771 64.3703 65.2838 64.1671 65.4971C63.964 65.7105 63.8624 66.0051 63.8624 66.381C63.8624 66.7416 63.9589 67.026 64.1519 67.2343C64.3449 67.4425 64.604 67.5467 64.929 67.5467C65.1627 67.5467 65.371 67.501 65.5538 67.4095C65.7367 67.313 65.8789 67.1784 65.9805 67.0057C66.0871 66.8279 66.1405 66.6222 66.1405 66.3886C66.1405 66.1549 66.0922 65.9467 65.9957 65.7638C65.9043 65.581 65.7697 65.4387 65.5919 65.3371C65.4192 65.2305 65.2186 65.1771 64.99 65.1771ZM69.5661 64.4686C70.1908 64.4686 70.6708 64.6794 71.0061 65.101C71.3464 65.5175 71.5165 66.1143 71.5165 66.8914C71.5165 67.5213 71.4429 68.0775 71.2956 68.56C71.1534 69.0375 70.9223 69.4184 70.6023 69.7029C70.2823 69.9822 69.8658 70.1219 69.3527 70.1219C68.8905 70.1219 68.5197 70.0076 68.2404 69.779C67.961 69.5454 67.7654 69.2254 67.6537 68.819L68.4232 68.7505C68.4892 68.9638 68.5959 69.1289 68.7432 69.2457C68.8905 69.3575 69.0937 69.4133 69.3527 69.4133C69.6372 69.4133 69.8835 69.3321 70.0918 69.1695C70.3 69.0019 70.46 68.753 70.5718 68.4229C70.6835 68.0927 70.7445 67.6838 70.7546 67.1962L70.907 67.2267C70.8207 67.5111 70.6429 67.7524 70.3737 67.9505C70.1045 68.1435 69.787 68.24 69.4213 68.24C69.0708 68.24 68.761 68.1638 68.4918 68.0114C68.2226 67.854 68.0143 67.6356 67.867 67.3562C67.7197 67.0717 67.6461 66.7492 67.6461 66.3886C67.6461 65.9924 67.7248 65.6521 67.8823 65.3676C68.0448 65.0781 68.2708 64.8571 68.5604 64.7048C68.855 64.5473 69.1902 64.4686 69.5661 64.4686ZM69.5585 65.1771C69.2131 65.1771 68.9388 65.2838 68.7356 65.4971C68.5324 65.7105 68.4308 66.0051 68.4308 66.381C68.4308 66.7416 68.5273 67.026 68.7204 67.2343C68.9134 67.4425 69.1724 67.5467 69.4975 67.5467C69.7312 67.5467 69.9394 67.501 70.1223 67.4095C70.3051 67.313 70.4473 67.1784 70.5489 67.0057C70.6556 66.8279 70.7089 66.6222 70.7089 66.3886C70.7089 66.1549 70.6607 65.9467 70.5642 65.7638C70.4727 65.581 70.3381 65.4387 70.1604 65.3371C69.9877 65.2305 69.787 65.1771 69.5585 65.1771ZM74.1345 64.4686C74.7593 64.4686 75.2393 64.6794 75.5745 65.101C75.9148 65.5175 76.085 66.1143 76.085 66.8914C76.085 67.5213 76.0113 68.0775 75.864 68.56C75.7218 69.0375 75.4907 69.4184 75.1707 69.7029C74.8507 69.9822 74.4342 70.1219 73.9212 70.1219C73.459 70.1219 73.0882 70.0076 72.8088 69.779C72.5294 69.5454 72.3339 69.2254 72.2221 68.819L72.9917 68.7505C73.0577 68.9638 73.1644 69.1289 73.3117 69.2457C73.459 69.3575 73.6621 69.4133 73.9212 69.4133C74.2056 69.4133 74.452 69.3321 74.6602 69.1695C74.8685 69.0019 75.0285 68.753 75.1402 68.4229C75.252 68.0927 75.3129 67.6838 75.3231 67.1962L75.4755 67.2267C75.3891 67.5111 75.2113 67.7524 74.9421 67.9505C74.6729 68.1435 74.3555 68.24 73.9898 68.24C73.6393 68.24 73.3294 68.1638 73.0602 68.0114C72.791 67.854 72.5828 67.6356 72.4355 67.3562C72.2882 67.0717 72.2145 66.7492 72.2145 66.3886C72.2145 65.9924 72.2933 65.6521 72.4507 65.3676C72.6133 65.0781 72.8393 64.8571 73.1288 64.7048C73.4234 64.5473 73.7587 64.4686 74.1345 64.4686ZM74.1269 65.1771C73.7815 65.1771 73.5072 65.2838 73.304 65.4971C73.1009 65.7105 72.9993 66.0051 72.9993 66.381C72.9993 66.7416 73.0958 67.026 73.2888 67.2343C73.4818 67.4425 73.7409 67.5467 74.066 67.5467C74.2996 67.5467 74.5079 67.501 74.6907 67.4095C74.8736 67.313 75.0158 67.1784 75.1174 67.0057C75.224 66.8279 75.2774 66.6222 75.2774 66.3886C75.2774 66.1549 75.2291 65.9467 75.1326 65.7638C75.0412 65.581 74.9066 65.4387 74.7288 65.3371C74.5561 65.2305 74.3555 65.1771 74.1269 65.1771ZM78.703 64.4686C79.3277 64.4686 79.8077 64.6794 80.143 65.101C80.4833 65.5175 80.6535 66.1143 80.6535 66.8914C80.6535 67.5213 80.5798 68.0775 80.4325 68.56C80.2903 69.0375 80.0592 69.4184 79.7392 69.7029C79.4192 69.9822 79.0027 70.1219 78.4896 70.1219C78.0274 70.1219 77.6566 70.0076 77.3773 69.779C77.0979 69.5454 76.9023 69.2254 76.7906 68.819L77.5601 68.7505C77.6262 68.9638 77.7328 69.1289 77.8801 69.2457C78.0274 69.3575 78.2306 69.4133 78.4896 69.4133C78.7741 69.4133 79.0204 69.3321 79.2287 69.1695C79.4369 69.0019 79.5969 68.753 79.7087 68.4229C79.8204 68.0927 79.8814 67.6838 79.8915 67.1962L80.0439 67.2267C79.9576 67.5111 79.7798 67.7524 79.5106 67.9505C79.2414 68.1435 78.9239 68.24 78.5582 68.24C78.2077 68.24 77.8979 68.1638 77.6287 68.0114C77.3595 67.854 77.1512 67.6356 77.0039 67.3562C76.8566 67.0717 76.783 66.7492 76.783 66.3886C76.783 65.9924 76.8617 65.6521 77.0192 65.3676C77.1817 65.0781 77.4077 64.8571 77.6973 64.7048C77.9919 64.5473 78.3271 64.4686 78.703 64.4686ZM78.6954 65.1771C78.35 65.1771 78.0757 65.2838 77.8725 65.4971C77.6693 65.7105 77.5677 66.0051 77.5677 66.381C77.5677 66.7416 77.6642 67.026 77.8573 67.2343C78.0503 67.4425 78.3093 67.5467 78.6344 67.5467C78.8681 67.5467 79.0763 67.501 79.2592 67.4095C79.442 67.313 79.5842 67.1784 79.6858 67.0057C79.7925 66.8279 79.8458 66.6222 79.8458 66.3886C79.8458 66.1549 79.7976 65.9467 79.7011 65.7638C79.6096 65.581 79.475 65.4387 79.2973 65.3371C79.1246 65.2305 78.9239 65.1771 78.6954 65.1771ZM82.1133 67.4476H84.46V68.1105H82.1133V67.4476ZM87.6875 66.099H86.278V65.4667H86.9865C87.1846 65.4667 87.3446 65.4387 87.4665 65.3829C87.5885 65.3219 87.6773 65.2279 87.7332 65.101C87.7891 64.974 87.817 64.8038 87.817 64.5905H88.4342V70H87.6875V66.099ZM85.9961 69.299H89.7142V70H85.9961V69.299ZM92.256 66.099H90.8464V65.4667H91.555C91.7531 65.4667 91.9131 65.4387 92.035 65.3829C92.1569 65.3219 92.2458 65.2279 92.3017 65.101C92.3575 64.974 92.3855 64.8038 92.3855 64.5905H93.0026V70H92.256V66.099ZM90.5645 69.299H94.2826V70H90.5645V69.299ZM96.992 70.1219C96.5958 70.1219 96.253 70.0102 95.9635 69.7867C95.679 69.5632 95.4581 69.2406 95.3006 68.819C95.1482 68.3924 95.072 67.887 95.072 67.3029C95.072 66.7187 95.1482 66.2133 95.3006 65.7867C95.4581 65.36 95.6815 65.0349 95.9711 64.8114C96.2606 64.5829 96.6009 64.4686 96.992 64.4686C97.3831 64.4686 97.7235 64.5829 98.013 64.8114C98.3025 65.0349 98.5235 65.36 98.6758 65.7867C98.8333 66.2133 98.912 66.7187 98.912 67.3029C98.912 67.887 98.8333 68.3924 98.6758 68.819C98.5235 69.2406 98.3025 69.5632 98.013 69.7867C97.7285 70.0102 97.3882 70.1219 96.992 70.1219ZM96.992 69.4133C97.2308 69.4133 97.4339 69.3295 97.6015 69.1619C97.7742 68.9943 97.9038 68.753 97.9901 68.4381C98.0815 68.1181 98.1273 67.7397 98.1273 67.3029C98.1273 66.866 98.0815 66.4876 97.9901 66.1676C97.9038 65.8476 97.7742 65.6038 97.6015 65.4362C97.4289 65.2635 97.2257 65.1771 96.992 65.1771C96.7584 65.1771 96.5552 65.2635 96.3825 65.4362C96.2098 65.6038 96.0777 65.8476 95.9863 66.1676C95.9 66.4876 95.8568 66.866 95.8568 67.3029C95.8568 67.7397 95.9 68.1181 95.9863 68.4381C96.0777 68.753 96.2073 68.9943 96.3749 69.1619C96.5476 69.3295 96.7533 69.4133 96.992 69.4133ZM97.7235 65.36L98.333 65.3981L96.2606 69.2305L95.6511 69.1924L97.7235 65.36Z"
        fill="#737373"
      />
      <rect
        x="86.5"
        y="92.5"
        width="21"
        height="21"
        rx="5.5"
        fill={BG_DEFAULT}
        stroke={BORDER_SUBTLE}
      />
      <path
        d="M96.9999 103.222C97.859 103.222 98.5554 102.526 98.5554 101.667C98.5554 100.808 97.859 100.111 96.9999 100.111C96.1408 100.111 95.4443 100.808 95.4443 101.667C95.4443 102.526 96.1408 103.222 96.9999 103.222Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M100.333 96.5557H93.6664C92.6846 96.5557 91.8887 97.3516 91.8887 98.3334V107.667C91.8887 108.649 92.6846 109.445 93.6664 109.445H100.333C101.315 109.445 102.111 108.649 102.111 107.667V98.3334C102.111 97.3516 101.315 96.5557 100.333 96.5557Z"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M95.2222 105.889H98.7777"
        stroke={CONTENT_SUBTLE}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M97 86L97 92" stroke={BORDER_SUBTLE} />
      <defs>
        <pattern
          id="pattern0_4494_38789"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use xlinkHref="#image0_4494_38789" transform="scale(0.0025)" />
        </pattern>
        <clipPath id="clip0_4494_38789">
          <rect
            width="7.61905"
            height="7.61905"
            fill={BG_DEFAULT}
            transform="translate(48 63.3809)"
          />
        </clipPath>
        <image
          id="image0_4494_38789"
          width="400"
          height="400"
          xlinkHref={logo}
        />
      </defs>
    </svg>
  );
};
