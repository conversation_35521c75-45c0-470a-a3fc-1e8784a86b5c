import { Link } from '@inertiajs/react';
import { cn } from '@/lib/utils';
import { User, Workspace } from '@/types';
import { 
  HomeIcon, 
  LinkIcon, 
  GlobeAltIcon, 
  ChartBarIcon,
  CogIcon,
  UsersIcon
} from '@heroicons/react/24/outline';

interface SidebarProps {
  user: User;
  workspace?: Workspace;
}

const navigation = [
  { name: 'Overview', href: '/dashboard', icon: HomeIcon },
  { name: 'Links', href: '/dashboard/links', icon: LinkIcon },
  { name: 'Analytics', href: '/dashboard/analytics', icon: ChartBarIcon },
  { name: 'Domains', href: '/dashboard/domains', icon: GlobeAltIcon },
  { name: 'Team', href: '/dashboard/team', icon: UsersIcon },
  { name: 'Settings', href: '/dashboard/settings', icon: CogIcon },
];

export default function Sidebar({ user, workspace }: SidebarProps) {
  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <div className="flex items-center">
            {workspace?.logo ? (
              <img
                className="h-8 w-8 rounded-md"
                src={workspace.logo}
                alt={workspace.name}
              />
            ) : (
              <div className="h-8 w-8 rounded-md bg-black flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {workspace?.name?.charAt(0) || 'D'}
                </span>
              </div>
            )}
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {workspace?.name || 'Dub'}
              </p>
              <p className="text-xs text-gray-500">
                {workspace?.plan || 'Free'}
              </p>
            </div>
          </div>
        </div>
        
        <div className="mt-5 flex-grow flex flex-col">
          <nav className="flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const isActive = window.location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    isActive
                      ? 'bg-gray-100 text-gray-900'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                    'group flex items-center px-2 py-2 text-sm font-medium rounded-md'
                  )}
                >
                  <item.icon
                    className={cn(
                      isActive ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500',
                      'mr-3 flex-shrink-0 h-5 w-5'
                    )}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
        
        <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
          <div className="flex items-center">
            <div>
              {user.image ? (
                <img
                  className="inline-block h-8 w-8 rounded-full"
                  src={user.image}
                  alt={user.name || 'User'}
                />
              ) : (
                <div className="inline-block h-8 w-8 rounded-full bg-gray-500 flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </span>
                </div>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">
                {user.name || user.email}
              </p>
              <p className="text-xs text-gray-500">
                {user.email}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
