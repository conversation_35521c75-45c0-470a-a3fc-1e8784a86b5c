[2025-05-25 09:27:38] local.ERROR: SQLSTATE[HY000]: General error: 1 table users has no column named password_hash (Connection: sqlite, SQL: insert into "users" ("id", "name", "email", "password_hash", "email_verified_at", "updated_at", "created_at") values (265e967f-4a51-40f6-88e2-90afd8e9257c, Test User, <EMAIL>, $2y$12$A5q2977hfYNKn2bKwmtYrOJwx9Mr1Rll9eGtm1VcD9NmtfrLa6Wxi, 2025-05-25 09:27:38, 2025-05-25 09:27:38, 2025-05-25 09:27:38)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named password_hash (Connection: sqlite, SQL: insert into \"users\" (\"id\", \"name\", \"email\", \"password_hash\", \"email_verified_at\", \"updated_at\", \"created_at\") values (265e967f-4a51-40f6-88e2-90afd8e9257c, Test User, <EMAIL>, $2y$12$A5q2977hfYNKn2bKwmtYrOJwx9Mr1Rll9eGtm1VcD9NmtfrLa6Wxi, 2025-05-25 09:27:38, 2025-05-25 09:27:38, 2025-05-25 09:27:38)) at /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#3 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3736): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#4 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1384): Illuminate\\Database\\Eloquent\\Builder->__call('insert', Array)
#6 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#7 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#8 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#9 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#10 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#11 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#12 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#13 /Users/<USER>/Applications/Sites/dub/laravel-dub/database/seeders/DubSeeder.php(21): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#14 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\DubSeeder->run()
#15 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#20 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#21 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#22 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#23 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#24 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#25 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#30 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Applications/Sites/dub/laravel-dub/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#38 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named password_hash at /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php:562)
[stacktrace]
#0 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(562): PDO->prepare('insert into \"us...')
#1 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"us...', Array)
#2 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#5 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3736): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#6 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insert(Array)
#7 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1384): Illuminate\\Database\\Eloquent\\Builder->__call('insert', Array)
#8 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#11 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#12 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 /Users/<USER>/Applications/Sites/dub/laravel-dub/database/seeders/DubSeeder.php(21): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\DubSeeder->run()
#17 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#25 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 /Users/<USER>/Applications/Sites/dub/laravel-dub/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}
"} 
[2025-05-25 09:28:24] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: workspace_users.user_id (Connection: sqlite, SQL: insert into "workspace_users" ("created_at", "id", "role", "updated_at", "user_id", "workspace_id") values (2025-05-25 09:28:24, c03bac3d-7330-4550-9466-4bbee27ef290, owner, 2025-05-25 09:28:24, ?, 5e7cae5f-4d9d-4969-801b-47456e844985), (2025-05-25 09:28:24, c03bac3d-7330-4550-9466-4bbee27ef290, owner, 2025-05-25 09:28:24, b65bd06b-7772-4976-b7ec-efb4ba574610, 5e7cae5f-4d9d-4969-801b-47456e844985)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: workspace_users.user_id (Connection: sqlite, SQL: insert into \"workspace_users\" (\"created_at\", \"id\", \"role\", \"updated_at\", \"user_id\", \"workspace_id\") values (2025-05-25 09:28:24, c03bac3d-7330-4550-9466-4bbee27ef290, owner, 2025-05-25 09:28:24, ?, 5e7cae5f-4d9d-4969-801b-47456e844985), (2025-05-25 09:28:24, c03bac3d-7330-4550-9466-4bbee27ef290, owner, 2025-05-25 09:28:24, b65bd06b-7772-4976-b7ec-efb4ba574610, 5e7cae5f-4d9d-4969-801b-47456e844985)) at /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"wo...', Array, Object(Closure))
#1 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"wo...', Array, Object(Closure))
#2 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"wo...', Array)
#3 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3736): Illuminate\\Database\\Connection->insert('insert into \"wo...', Array)
#4 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Concerns/InteractsWithPivotTable.php(270): Illuminate\\Database\\Query\\Builder->insert(Array)
#5 /Users/<USER>/Applications/Sites/dub/laravel-dub/database/seeders/DubSeeder.php(41): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->attach(Object(Ramsey\\Uuid\\Lazy\\LazyUuidFromString), Array)
#6 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\DubSeeder->run()
#7 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#12 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#13 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#14 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#15 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#16 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#17 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Applications/Sites/dub/laravel-dub/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: workspace_users.user_id at /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"wo...', Array)
#2 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"wo...', Array, Object(Closure))
#3 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"wo...', Array, Object(Closure))
#4 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"wo...', Array)
#5 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3736): Illuminate\\Database\\Connection->insert('insert into \"wo...', Array)
#6 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Concerns/InteractsWithPivotTable.php(270): Illuminate\\Database\\Query\\Builder->insert(Array)
#7 /Users/<USER>/Applications/Sites/dub/laravel-dub/database/seeders/DubSeeder.php(41): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->attach(Object(Ramsey\\Uuid\\Lazy\\LazyUuidFromString), Array)
#8 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Database\\Seeders\\DubSeeder->run()
#9 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#14 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#15 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#16 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#17 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Database/Console/Seeds/SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#18 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#19 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/Applications/Sites/dub/laravel-dub/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/Applications/Sites/dub/laravel-dub/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
